<div class="flex h-screen text-sm overflow-hidden">
  <!-- Vertical Tab Bar -->
  <div class="w-48 bg-gray-800 border-r border-gray-700 flex flex-col">
    <!-- Tab List -->
    <div class="flex-1 overflow-y-auto">
      <div
        *ngFor="let tab of tabService.tabsValue"
        class="flex items-center justify-between p-3 border-b border-gray-700 cursor-pointer hover:bg-gray-700"
        [ngClass]="{ 'bg-gray-700': tab.isActive }"
        (click)="setActiveTab(tab.id)"
      >
        <span class="text-white truncate">{{ tab.name }}</span>
        <button
          class="text-red-400 hover:text-red-300 ml-2"
          (click)="deleteTab(tab.id); $event.stopPropagation()"
        >
          ×
        </button>
      </div>
    </div>

    <!-- Add Tab Button -->
    <div class="p-3 border-t border-gray-700">
      <button
        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
        (click)="createTab()"
      >
        + New Tab
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <div
      *ngIf="tabService.tabsValue.length === 0"
      class="flex-1 flex items-center justify-center text-white"
    >
      <div class="text-center">
        <p class="text-lg mb-4">No tabs open</p>
        <button
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          (click)="createTab()"
        >
          Create your first tab
        </button>
      </div>
    </div>

    <div
      *ngFor="let tab of tabService.tabsValue"
      [hidden]="!tab.isActive"
      class="flex-1 flex flex-col p-3 overflow-hidden"
    >
      <!-- Tab Controls -->
      <div class="text-white mb-3">
        <div class="w-full flex justify-start align-center gap-2 mb-2">
          <button
            [ngClass]="{
              underline: tab.currentAction === Action.Pull
            }"
            (click)="
              tab.currentAction !== Action.Pull
                ? pullTab(tab.id)
                : stopCommandTab(tab.id)
            "
            [disabled]="!validateTabProfileIndex(tab)"
          >
            {{ tab.currentAction === Action.Pull ? "Stop" : "Pull" }}
          </button>
          <button
            [ngClass]="{
              underline: tab.currentAction === Action.Push
            }"
            (click)="
              tab.currentAction !== Action.Push
                ? pushTab(tab.id)
                : stopCommandTab(tab.id)
            "
            [disabled]="!validateTabProfileIndex(tab)"
          >
            {{ tab.currentAction === Action.Push ? "Stop" : "Push" }}
          </button>
          <button
            [ngClass]="{
              underline: tab.currentAction === Action.Bi
            }"
            (click)="
              tab.currentAction !== Action.Bi
                ? biTab(tab.id)
                : stopCommandTab(tab.id)
            "
            [disabled]="!validateTabProfileIndex(tab)"
          >
            {{ tab.currentAction === Action.Bi ? "Stop" : "Sync" }}
          </button>
          <button
            [ngClass]="{
              underline: tab.currentAction === Action.BiResync
            }"
            (click)="
              tab.currentAction !== Action.BiResync
                ? biResyncTab(tab.id)
                : stopCommandTab(tab.id)
            "
            [disabled]="!validateTabProfileIndex(tab)"
          >
            {{ tab.currentAction === Action.BiResync ? "Stop" : "Resync" }}
          </button>
        </div>

        <!-- Profile Selector -->
        <select
          (change)="changeProfileTab($event, tab.id)"
          class="bg-gray-700 text-white border border-gray-600 rounded px-2 py-1"
        >
          <option [value]="null">Profile is not selected</option>
          <option
            *ngFor="
              let profile of appService.configInfo$.value.profiles;
              let idx = index
            "
            [value]="idx"
            [selected]="tab.selectedProfileIndex === idx"
          >
            {{ profile.name }}
          </option>
        </select>
      </div>

      <!-- Working Directory -->
      <code class="text-white overflow-hidden mb-3">
        <pre>
Working directory: {{ (appService.configInfo$ | async)?.working_dir }}</pre
        >
      </code>

      <!-- Tab Output -->
      <code class="text-white flex-1 overflow-auto">
        <pre>{{ tab.data.join("\n") }}</pre>
      </code>
    </div>
  </div>
</div>
