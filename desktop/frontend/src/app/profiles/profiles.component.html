<ul
  class="text-white text-sm h-screen p-3 pl-0 flex flex-col items-start gap-3"
>
  <section class="flex gap-2 w-full">
    <button (click)="addProfile()">Add +</button>
    <div class="grow"></div>
    <button (click)="saveConfigInfo()">
      {{ saveBtnText$ | async }}
    </button>
  </section>
  <ul
    class="w-full grow bg-[var(--color-accent)] overflow-x-hidden overflow-y-auto rounded-md px-3 scrollbar scrollbar-thumb-gray-900 scrollbar-track-[var(--color-accent)]"
  >
    <li
      *ngFor="
        let setting of (appService.configInfo$ | async)?.profiles;
        let idx = index
      "
    >
      <details class="my-3">
        <summary>
          {{ setting.name || "Chưa có tiêu đề" }}
        </summary>
        <ul class="w-full mt-2 shadow-none">
          <div class="flex flex-col gap-1">
            <label>Name</label>
            <input
              type="text"
              [(ngModel)]="setting.name"
              placeholder="Chưa có tiêu đề"
            />
          </div>
          <section class="grid grid-cols-2 gap-6 mt-6">
            <div class="flex flex-col gap-1">
              <label>From path</label>
              <div class="flex gap-2">
                <select
                  [ngModel]="getFromRemote(setting)"
                  (ngModelChange)="
                    updateFromPath(setting, $event, getFromPath(setting))
                  "
                  class="w-32"
                >
                  <option value="">Local</option>
                  <option
                    *ngFor="let remote of appService.remotes$ | async"
                    [value]="remote.name"
                  >
                    {{ remote.name }}
                  </option>
                </select>
                <input
                  type="text"
                  [ngModel]="getFromPath(setting)"
                  (ngModelChange)="
                    updateFromPath(setting, getFromRemote(setting), $event)
                  "
                  placeholder="/drive"
                  class="flex-1"
                />
              </div>
            </div>
            <div class="flex flex-col gap-1">
              <label>To path</label>
              <div class="flex gap-2">
                <select
                  [ngModel]="getToRemote(setting)"
                  (ngModelChange)="
                    updateToPath(setting, $event, getToPath(setting))
                  "
                  class="w-32"
                >
                  <option value="">Local</option>
                  <option
                    *ngFor="let remote of appService.remotes$ | async"
                    [value]="remote.name"
                  >
                    {{ remote.name }}
                  </option>
                </select>
                <input
                  type="text"
                  [ngModel]="getToPath(setting)"
                  (ngModelChange)="
                    updateToPath(setting, getToRemote(setting), $event)
                  "
                  placeholder="./drive"
                  class="flex-1"
                />
              </div>
            </div>
            <div class="flex flex-col gap-1">
              <label>Backup path</label>
              <input
                type="text"
                [(ngModel)]="setting.backup_path"
                placeholder=".backup"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>Cache path</label>
              <input
                type="text"
                [(ngModel)]="setting.cache_path"
                placeholder=".cache"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>Parallel</label>
              <select [(ngModel)]="setting.parallel">
                <option *ngFor="let i of getNumberRange(1, 32)" [value]="i">
                  {{ i }}
                </option>
              </select>
            </div>
            <div class="flex flex-col gap-1">
              <label>Bandwidth (MB/s)</label>
              <select [(ngModel)]="setting.bandwidth">
                <option *ngFor="let i of getNumberRange(1, 100)" [value]="i">
                  {{ i }}
                </option>
              </select>
            </div>
            <ul>
              <li>
                <label>Include paths</label>
              </li>
              <li
                *ngFor="
                  let s of setting.included_paths;
                  trackBy: trackByFn;
                  let idx2 = index
                "
                class="flex gap-2"
              >
                <select
                  [ngModel]="getIncludePathType(setting, idx2)"
                  (ngModelChange)="updateIncludePathType(setting, idx2, $event)"
                  class="w-20"
                >
                  <option value="folder">Folder</option>
                  <option value="file">File</option>
                </select>
                <input
                  type="text"
                  [ngModel]="getIncludePathValue(setting, idx2)"
                  (ngModelChange)="
                    updateIncludePathValue(setting, idx2, $event)
                  "
                  placeholder="/included"
                  class="flex-1"
                />
                <button
                  class="border-red-500"
                  (click)="removeIncludePath(idx, idx2)"
                >
                  ✕
                </button>
              </li>
              <li>
                <button (click)="addIncludePath(idx)">+</button>
              </li>
            </ul>
            <ul>
              <li>
                <label>Exclude paths</label>
              </li>
              <li
                *ngFor="
                  let s of setting.excluded_paths;
                  trackBy: trackByFn;
                  let idx2 = index
                "
                class="flex gap-2"
              >
                <select
                  [ngModel]="getExcludePathType(setting, idx2)"
                  (ngModelChange)="updateExcludePathType(setting, idx2, $event)"
                  class="w-20"
                >
                  <option value="folder">Folder</option>
                  <option value="file">File</option>
                </select>
                <input
                  type="text"
                  [ngModel]="getExcludePathValue(setting, idx2)"
                  (ngModelChange)="
                    updateExcludePathValue(setting, idx2, $event)
                  "
                  placeholder="/excluded"
                  class="flex-1"
                />
                <button
                  class="border-red-500"
                  (click)="removeExcludePath(idx, idx2)"
                >
                  ✕
                </button>
              </li>
              <li>
                <button (click)="addExcludePath(idx)">+</button>
              </li>
            </ul>
          </section>
          <section class="flex justify-start mt-6">
            <button class="delete-btn" (click)="removeProfile(idx)">
              Delete
            </button>
          </section>
        </ul>
      </details>
    </li>
  </ul>
</ul>
